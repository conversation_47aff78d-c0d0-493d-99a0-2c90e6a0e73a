<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .actor-label { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #2c3e50; }
      .message-label { font-family: Arial, sans-serif; font-size: 10px; fill: #34495e; }
      .note-label { font-family: Arial, sans-serif; font-size: 9px; fill: #7f8c8d; }
      .actor-box { fill: #ecf0f1; stroke: #34495e; stroke-width: 2; }
      .lifeline { stroke: #bdc3c7; stroke-width: 1; stroke-dasharray: 5,5; }
      .message-arrow { stroke: #2980b9; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #27ae60; stroke-width: 1; fill: none; marker-end: url(#arrowhead-small); stroke-dasharray: 3,3; }
      .async-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .note-box { fill: #fff3cd; stroke: #ffc107; stroke-width: 1; }
      .activation { fill: #d5dbdb; stroke: #85929e; stroke-width: 1; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2980b9" />
    </marker>
    <marker id="arrowhead-small" markerWidth="8" markerHeight="5" refX="7" refY="2.5" orient="auto">
      <polygon points="0 0, 8 2.5, 0 5" fill="#27ae60" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">Supplemental Data Processing - Sequence Diagram</text>
  
  <!-- Actors -->
  <g id="actors">
    <!-- ABA User -->
    <rect x="50" y="60" width="80" height="40" class="actor-box" rx="5"/>
    <text x="90" y="85" text-anchor="middle" class="actor-label">ABA User</text>
    
    <!-- Admin Console -->
    <rect x="170" y="60" width="80" height="40" class="actor-box" rx="5"/>
    <text x="210" y="80" text-anchor="middle" class="actor-label">Admin</text>
    <text x="210" y="95" text-anchor="middle" class="actor-label">Console</text>
    
    <!-- Upload Service -->
    <rect x="290" y="60" width="80" height="40" class="actor-box" rx="5"/>
    <text x="330" y="80" text-anchor="middle" class="actor-label">Upload</text>
    <text x="330" y="95" text-anchor="middle" class="actor-label">Service</text>
    
    <!-- Message Queue -->
    <rect x="410" y="60" width="80" height="40" class="actor-box" rx="5"/>
    <text x="450" y="80" text-anchor="middle" class="actor-label">Message</text>
    <text x="450" y="95" text-anchor="middle" class="actor-label">Queue</text>
    
    <!-- Merge Engine -->
    <rect x="530" y="60" width="80" height="40" class="actor-box" rx="5"/>
    <text x="570" y="80" text-anchor="middle" class="actor-label">Merge</text>
    <text x="570" y="95" text-anchor="middle" class="actor-label">Engine</text>
    
    <!-- Mapping Service -->
    <rect x="650" y="60" width="80" height="40" class="actor-box" rx="5"/>
    <text x="690" y="80" text-anchor="middle" class="actor-label">Mapping</text>
    <text x="690" y="95" text-anchor="middle" class="actor-label">Service</text>
    
    <!-- File Storage -->
    <rect x="770" y="60" width="80" height="40" class="actor-box" rx="5"/>
    <text x="810" y="80" text-anchor="middle" class="actor-label">File</text>
    <text x="810" y="95" text-anchor="middle" class="actor-label">Storage</text>
    
    <!-- Opportunity Service -->
    <rect x="890" y="60" width="80" height="40" class="actor-box" rx="5"/>
    <text x="930" y="80" text-anchor="middle" class="actor-label">Opportunity</text>
    <text x="930" y="95" text-anchor="middle" class="actor-label">Service</text>
    
    <!-- Notification Service -->
    <rect x="1010" y="60" width="80" height="40" class="actor-box" rx="5"/>
    <text x="1050" y="80" text-anchor="middle" class="actor-label">Notification</text>
    <text x="1050" y="95" text-anchor="middle" class="actor-label">Service</text>
    
    <!-- External System -->
    <rect x="1130" y="60" width="80" height="40" class="actor-box" rx="5"/>
    <text x="1170" y="80" text-anchor="middle" class="actor-label">External</text>
    <text x="1170" y="95" text-anchor="middle" class="actor-label">System</text>
  </g>
  
  <!-- Lifelines -->
  <g id="lifelines">
    <line x1="90" y1="100" x2="90" y2="850" class="lifeline"/>
    <line x1="210" y1="100" x2="210" y2="850" class="lifeline"/>
    <line x1="330" y1="100" x2="330" y2="850" class="lifeline"/>
    <line x1="450" y1="100" x2="450" y2="850" class="lifeline"/>
    <line x1="570" y1="100" x2="570" y2="850" class="lifeline"/>
    <line x1="690" y1="100" x2="690" y2="850" class="lifeline"/>
    <line x1="810" y1="100" x2="810" y2="850" class="lifeline"/>
    <line x1="930" y1="100" x2="930" y2="850" class="lifeline"/>
    <line x1="1050" y1="100" x2="1050" y2="850" class="lifeline"/>
    <line x1="1170" y1="100" x2="1170" y2="850" class="lifeline"/>
  </g>
  
  <!-- Sequence Messages -->
  <g id="messages">
    <!-- 1. Upload supplemental files -->
    <line x1="90" y1="130" x2="210" y2="130" class="message-arrow"/>
    <text x="150" y="125" text-anchor="middle" class="message-label">1. Upload supplemental files</text>
    
    <!-- 2. Validate and process -->
    <line x1="210" y1="150" x2="330" y2="150" class="message-arrow"/>
    <text x="270" y="145" text-anchor="middle" class="message-label">2. POST /upload/supplemental</text>
    
    <!-- Activation box for Upload Service -->
    <rect x="325" y="150" width="10" height="120" class="activation"/>
    
    <!-- 3. Validate file -->
    <line x1="330" y1="170" x2="330" y2="170" class="message-arrow"/>
    <text x="380" y="165" class="message-label">3. Validate file format/schema</text>
    
    <!-- 4. Store file -->
    <line x1="330" y1="190" x2="810" y2="190" class="message-arrow"/>
    <text x="570" y="185" text-anchor="middle" class="message-label">4. Store supplemental file</text>
    
    <!-- 5. Publish event -->
    <line x1="330" y1="210" x2="450" y2="210" class="async-arrow"/>
    <text x="390" y="205" text-anchor="middle" class="message-label">5. Publish upload event</text>
    
    <!-- 6. Return confirmation -->
    <line x1="330" y1="230" x2="210" y2="230" class="return-arrow"/>
    <text x="270" y="225" text-anchor="middle" class="message-label">6. Upload confirmation</text>
    
    <!-- 7. Show status -->
    <line x1="210" y1="250" x2="90" y2="250" class="return-arrow"/>
    <text x="150" y="245" text-anchor="middle" class="message-label">7. Show upload status</text>
    
    <!-- Note about XML arrival -->
    <rect x="1100" y="280" width="140" height="40" class="note-box" rx="3"/>
    <text x="1170" y="295" text-anchor="middle" class="note-label">XML file uploaded to S3</text>
    <text x="1170" y="310" text-anchor="middle" class="note-label">by LTU/AQE/SLIM</text>

    <!-- 8. S3 event notification -->
    <line x1="1170" y1="340" x2="450" y2="340" class="async-arrow"/>
    <text x="810" y="335" text-anchor="middle" class="message-label">8. S3 event notification (XML uploaded)</text>

    <!-- 9. Trigger merge -->
    <line x1="450" y1="380" x2="570" y2="380" class="message-arrow"/>
    <text x="510" y="375" text-anchor="middle" class="message-label">9. Process merge event</text>
    
    <!-- Activation box for Merge Engine -->
    <rect x="565" y="380" width="10" height="200" class="activation"/>

    <!-- 10. Get supplemental files -->
    <line x1="570" y1="400" x2="810" y2="400" class="message-arrow"/>
    <text x="690" y="395" text-anchor="middle" class="message-label">10. Retrieve supplemental files</text>

    <!-- 11. Get XML file from S3 -->
    <line x1="570" y1="420" x2="810" y2="420" class="message-arrow"/>
    <text x="690" y="415" text-anchor="middle" class="message-label">11. Retrieve XML file from S3</text>

    <!-- 12. Get mapping config -->
    <line x1="570" y1="440" x2="690" y2="440" class="message-arrow"/>
    <text x="630" y="435" text-anchor="middle" class="message-label">12. Get mapping config</text>

    <!-- 13. Return mapping rules -->
    <line x1="690" y1="460" x2="570" y2="460" class="return-arrow"/>
    <text x="630" y="455" text-anchor="middle" class="message-label">13. Return mapping rules</text>

    <!-- 14. Execute merge -->
    <line x1="570" y1="480" x2="570" y2="480" class="message-arrow"/>
    <text x="620" y="475" class="message-label">14. Execute merge logic</text>

    <!-- 15. Store merged result -->
    <line x1="570" y1="500" x2="810" y2="500" class="message-arrow"/>
    <text x="690" y="495" text-anchor="middle" class="message-label">15. Store merged result</text>

    <!-- 16. Send to Opportunity Service -->
    <line x1="570" y1="520" x2="930" y2="520" class="message-arrow"/>
    <text x="750" y="515" text-anchor="middle" class="message-label">16. POST merged opportunity data</text>

    <!-- Activation box for Opportunity Service -->
    <rect x="925" y="520" width="10" height="40" class="activation"/>

    <!-- 17. Confirm receipt -->
    <line x1="930" y1="540" x2="570" y2="540" class="return-arrow"/>
    <text x="750" y="535" text-anchor="middle" class="message-label">17. Confirm data received</text>

    <!-- 18. Send notification -->
    <line x1="570" y1="560" x2="1050" y2="560" class="async-arrow"/>
    <text x="810" y="555" text-anchor="middle" class="message-label">18. Send success notification</text>

    <!-- Activation box for Notification Service -->
    <rect x="1045" y="560" width="10" height="60" class="activation"/>

    <!-- 19. Notify user -->
    <line x1="1050" y1="600" x2="90" y2="600" class="async-arrow"/>
    <text x="570" y="595" text-anchor="middle" class="message-label">19. Email/Slack notification</text>
  </g>
  
  <!-- Error Handling Section -->
  <g id="error-handling">
    <text x="100" y="700" class="actor-label">Error Handling Scenarios:</text>
    
    <!-- Retry logic -->
    <rect x="80" y="720" width="300" height="30" class="note-box" rx="3"/>
    <text x="90" y="735" class="note-label">• Automatic retry (up to 3 attempts) for transient failures</text>
    <text x="90" y="745" class="note-label">• Exponential backoff strategy for retry delays</text>
    
    <!-- Circuit breaker -->
    <rect x="80" y="760" width="300" height="30" class="note-box" rx="3"/>
    <text x="90" y="775" class="note-label">• Circuit breaker pattern for external service calls</text>
    <text x="90" y="785" class="note-label">• Fallback to queue for later processing</text>
    
    <!-- Alerting -->
    <rect x="80" y="800" width="300" height="30" class="note-box" rx="3"/>
    <text x="90" y="815" class="note-label">• Immediate alerts for persistent failures</text>
    <text x="90" y="825" class="note-label">• Escalation to support team via Slack/email</text>
  </g>
  
  <!-- Performance Notes -->
  <g id="performance-notes">
    <text x="500" y="700" class="actor-label">Performance Considerations:</text>
    
    <rect x="480" y="720" width="350" height="30" class="note-box" rx="3"/>
    <text x="490" y="735" class="note-label">• Asynchronous processing for non-blocking operations</text>
    <text x="490" y="745" class="note-label">• Message queues for decoupling and scalability</text>
    
    <rect x="480" y="760" width="350" height="30" class="note-box" rx="3"/>
    <text x="490" y="775" class="note-label">• File streaming for large file processing</text>
    <text x="490" y="785" class="note-label">• Caching for frequently accessed mapping configurations</text>
    
    <rect x="480" y="800" width="350" height="30" class="note-box" rx="3"/>
    <text x="490" y="815" class="note-label">• Auto-scaling based on queue depth and processing time</text>
    <text x="490" y="825" class="note-label">• Load balancing across multiple service instances</text>
  </g>
</svg>
