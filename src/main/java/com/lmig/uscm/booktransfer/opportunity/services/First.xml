<ACORD>
  <BookTransferMeta>
    <creation>
      <type>xmlExtraction</type>
      <origin>AQE</origin>
    </creation>
  </BookTransferMeta>
  <SignonRq>
    <SignonPswd>
      <CustId>
        <SPName>AMSServices.com</SPName>
        <CustPermId/>
        <CustLoginId><EMAIL></CustLoginId>
      </CustId>
      <CustPswd>
        <EncryptionTypeCd>NONE</EncryptionTypeCd>
        <Pswd/>
      </CustPswd>
    </SignonPswd>
    <ClientDt>7/11/2023 12:52 PM</ClientDt>
    <CustLangPref>en-US</CustLangPref>
    <ClientApp>
      <Org>AMS Services</Org>
      <Name>Transit</Name>
      <Version>V2.5.5</Version>
    </ClientApp>
    <ProxyClient>
      <Org>BCFTech</Org>
      <Name>TransmitXML</Name>
      <Version>V1.00</Version>
    </ProxyClient>
  </SignonRq>
  <InsuranceSvcRq BypassScrubbing="SkipNaming">
    <RqUID>19191156-5738-4483-8E24-16B4CA8C6352</RqUID>
    <CommlAutoPolicyQuoteInqRq>
      <RqUID>CC11369C-EEA7-4933-A5BE-BC0D5B37E0A9</RqUID>
      <TransactionRequestDt>2023-07-11</TransactionRequestDt>
      <TransactionEffectiveDt>2023-05-03</TransactionEffectiveDt>
      <CurCd>USD</CurCd>
      <Producer>
        <ProducerInfo>
          <ContractNumber>92322023</ContractNumber>
          <ProducerRoleCd>Agency</ProducerRoleCd>
        </ProducerInfo>
      </Producer>
      <InsuredOrPrincipal>
        <ItemIdInfo>
          <InsurerId>3446F5146</InsurerId>
          <OtherIdentifier>
            <OtherIdTypeCd>Insured</OtherIdTypeCd>
            <OtherId>APPLIED</OtherId>
          </OtherIdentifier>
        </ItemIdInfo>
        <GeneralPartyInfo>
          <NameInfo>
            <PersonName>
              <Surname>SALLY</Surname>
              <GivenName>BRANDSTETTER</GivenName>
            </PersonName>
            <CommlName>
              <CommercialName>BRANDSTETTER SALLY</CommercialName>
            </CommlName>
            <LegalEntityCd>CP</LegalEntityCd>
          </NameInfo>
          <Addr>
          <AddrTypeCd>MailingAddress</AddrTypeCd>
          <Addr1>906 NEW YORK ST </Addr1>
          <City>LONGVIEW</City>
          <StateProvCd>WA</StateProvCd>
          <PostalCode>986324132</PostalCode>
        </Addr>
        <Communications>
          <PhoneInfo>
            <PhoneTypeCd>Phone</PhoneTypeCd>
            <CommunicationUseCd>Day</CommunicationUseCd>
            <PhoneNumber>******-8675309</PhoneNumber>
          </PhoneInfo>
          <PhoneInfo>
            <PhoneTypeCd>Phone</PhoneTypeCd>
            <CommunicationUseCd>Day</CommunicationUseCd>
            <PhoneNumber>******-8675309</PhoneNumber>
          </PhoneInfo>
          <PhoneInfo>
            <PhoneTypeCd>Phone</PhoneTypeCd>
            <CommunicationUseCd>Day</CommunicationUseCd>
            <PhoneNumber>******-8675309</PhoneNumber>
          </PhoneInfo>
        </Communications>
      </GeneralPartyInfo>
      <InsuredOrPrincipalInfo>
        <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
        <BusinessInfo>
          <SICCd>8712</SICCd>
          <NAICSCd>541310</NAICSCd>
          <NumEmployees>5</NumEmployees>
        </BusinessInfo>
      </InsuredOrPrincipalInfo>
    </InsuredOrPrincipal>
    <CommlPolicy id="PolicyLevel_M36">
      <PolicyNumber>UMC0002241</PolicyNumber>
      <LOBCd>AUTOB</LOBCd>
      <LOBSubCd>NPO</LOBSubCd>
      <NAICCd>99999</NAICCd>
      <ControllingStateProvCd>OR</ControllingStateProvCd>
      <ContractTerm>
        <EffectiveDt>2025-05-03</EffectiveDt>
        <ExpirationDt>2026-05-03</ExpirationDt>
        <DurationPeriod>
          <NumUnits>12</NumUnits>
          <UnitMeasurementCd>MON</UnitMeasurementCd>
        </DurationPeriod>
      </ContractTerm>
      <BillingMethodCd>CPB</BillingMethodCd>
      <CurrentTermAmt>
        <Amt>2900.00</Amt>
      </CurrentTermAmt>
      <LanguageCd>E</LanguageCd>
      <RateEffectiveDt>2023-05-03</RateEffectiveDt>
      <PaymentOption>
        <PaymentPlanCd>OT</PaymentPlanCd>
      </PaymentOption>
      <CommlPolicySupplement>
        <AuditInd>1</AuditInd>
        <NumEmployees>5</NumEmployees>
        <AuditFrequencyCd>XP</AuditFrequencyCd>
      </CommlPolicySupplement>
    </CommlPolicy>
    <Location id="L1_M36">
      <ItemIdInfo>
        <AgencyId>0001</AgencyId>
      </ItemIdInfo>
      <Addr>
        <AddrTypeCd>MailingAddress</AddrTypeCd>
        <Addr1>906 NEW YORK ST</Addr1>
        <City>LONGVIEW</City>
        <StateProvCd>WA</StateProvCd>
        <PostalCode>986324132</PostalCode>
      </Addr>
    </Location>
    <CommlAutoLineBusiness>
      <LOBCd>AUTOB</LOBCd>
      <LOBSubCd>NPO</LOBSubCd>
      <NAICCd>99999</NAICCd>
      <CurrentTermAmt>
        <Amt>2900.00</Amt>
      </CurrentTermAmt>
      <NetChangeAmt>
        <Amt>2900.00</Amt>
      </NetChangeAmt>
      <RateEffectiveDt>2023-05-03</RateEffectiveDt>
      <CoveredAutoSymbol>
        <CollisionSymbolCd>07</CollisionSymbolCd>
        <ComprehensiveSymbolCd>07</ComprehensiveSymbolCd>
        <EffectiveDt>2023-05-03</EffectiveDt>
        <LiabilitySymbolCd>01</LiabilitySymbolCd>
        <PIPNoFaultSymbolCd>05</PIPNoFaultSymbolCd>
        <UninsuredMotoristSymbolCd>02</UninsuredMotoristSymbolCd>
      </CoveredAutoSymbol>
      <CommlRateState>
        <StateProvCd>OR</StateProvCd>
        <CommlVeh LocationRef="L1_M36" id="V1_M36">
          <ItemIdInfo>
            <InsurerId>0001</InsurerId>
          </ItemIdInfo>
          <Manufacturer>AUDI</Manufacturer>
          <Model>Q5</Model>
          <ModelYear>2013</ModelYear>
          <VehTypeCd>PP</VehTypeCd>
          <Registration>
            <RegistrationId>Unknown</RegistrationId>
            <StateProvCd>OR</StateProvCd>
          </Registration>
          <CostNewAmt>
            <Amt>35900</Amt>
          </CostNewAmt>
          <FullTermAmt>
            <Amt>2143.00</Amt>
          </FullTermAmt>
          <RegistrationStateProvCd>OR</RegistrationStateProvCd>
          <TerritoryCd>110</TerritoryCd>
          <VehIdentificationNumber>WA1CFAFP7DA011139</VehIdentificationNumber>
          <VehSymbolCd>11</VehSymbolCd>
          <CommlVehSupplement>
            <FleetInd>0</FleetInd>
            <NewVehInd>0</NewVehInd>
            <PrimaryClassCd>739</PrimaryClassCd>
            <SelfPropelledVehInd>1</SelfPropelledVehInd>
            <VehAlteredInd>0</VehAlteredInd>
            <Addr>
              <AddrTypeCd>GaragingAddress</AddrTypeCd>
              <City>Portland</City>
              <StateProvCd>OR</StateProvCd>
              <PostalCode>97209</PostalCode>
            </Addr>
            <VehTypeCd>PP</VehTypeCd>
          </CommlVehSupplement>
          <CommlCoverage>
            <CoverageCd>PIP</CoverageCd>
            <CoverageDesc>No Fault</CoverageDesc>
            <CurrentTermAmt>
              <Amt>63.00</Amt>
            </CurrentTermAmt>
            <CreditOrSurcharge>
              <CreditSurchargeCd>XX</CreditSurchargeCd>
              <NumericValue>
                <FormatModFactor>1.587</FormatModFactor>
              </NumericValue>
            </CreditOrSurcharge>
            <CommlCoverageSupplement>
              <RatingFactor>1.587</RatingFactor>
            </CommlCoverageSupplement>
          </CommlCoverage>
          <CommlCoverage>
            <CoverageCd>ROAD</CoverageCd>
            <CoverageDesc>Auto Roadside Assistance Coverage</CoverageDesc>
            <CurrentTermAmt>
              <Amt>25.00</Amt>
            </CurrentTermAmt>
          </CommlCoverage>
          <CommlCoverage>
            <CoverageCd>COLL</CoverageCd>
            <CoverageDesc>Auto Collision Coverage</CoverageDesc>
            <Deductible btId="c301abad-0446-47de-9f9d-aa2da0f7e041">
              <FormatInteger>500</FormatInteger>
              <DeductibleTypeCd>FL</DeductibleTypeCd>
            </Deductible>
            <CurrentTermAmt>
              <Amt>375.00</Amt>
            </CurrentTermAmt>
            <CreditOrSurcharge>
              <CreditSurchargeCd>XX</CreditSurchargeCd>
              <NumericValue>
                <FormatModFactor>1.1</FormatModFactor>
              </NumericValue>
            </CreditOrSurcharge>
            <CommlCoverageSupplement>
              <RatingFactor>1.1</RatingFactor>
            </CommlCoverageSupplement>
          </CommlCoverage>
          <CommlCoverage>
            <CoverageCd>COMP</CoverageCd>
            <CoverageDesc>Auto Comprehensive Coverage</CoverageDesc>
            <Deductible btId="1373de74-29de-4a70-8a94-8863079ac26b">
              <FormatInteger>500</FormatInteger>
              <DeductibleTypeCd>FL</DeductibleTypeCd>
            </Deductible>
            <CurrentTermAmt>
              <Amt>111.00</Amt>
            </CurrentTermAmt>
            <CreditOrSurcharge>
              <CreditSurchargeCd>XX</CreditSurchargeCd>
              <NumericValue>
                <FormatModFactor>1.25</FormatModFactor>
              </NumericValue>
            </CreditOrSurcharge>
            <CommlCoverageSupplement>
              <RatingFactor>1.25</RatingFactor>
            </CommlCoverageSupplement>
          </CommlCoverage>
          <CommlCoverage>
            <CoverageCd>UMISG</CoverageCd>
            <CoverageDesc>Uninsured Motorist and Underinsured Motorist Bodily Injury</CoverageDesc>
            <Limit>
              <FormatInteger>1000000</FormatInteger>
              <LimitAppliesToCd>PerAcc</LimitAppliesToCd>
            </Limit>
            <CurrentTermAmt>
              <Amt>156.00</Amt>
            </CurrentTermAmt>
            <CreditOrSurcharge>
              <CreditSurchargeCd>XX</CreditSurchargeCd>
              <NumericValue>
                <FormatModFactor>1</FormatModFactor>
              </NumericValue>
            </CreditOrSurcharge>
            <CommlCoverageSupplement>
              <RatingFactor>1</RatingFactor>
            </CommlCoverageSupplement>
          </CommlCoverage>
          <CommlCoverage>
            <CoverageCd>CSL</CoverageCd>
            <CoverageDesc>Liability</CoverageDesc>
            <Limit>
              <FormatInteger>1000000</FormatInteger>
            </Limit>
            <CurrentTermAmt>
              <Amt>1413.00</Amt>
            </CurrentTermAmt>
            <CreditOrSurcharge>
              <CreditSurchargeCd>XX</CreditSurchargeCd>
              <NumericValue>
                <FormatModFactor>1.749</FormatModFactor>
              </NumericValue>
            </CreditOrSurcharge>
            <CommlCoverageSupplement>
              <RatingFactor>1.749</RatingFactor>
            </CommlCoverageSupplement>
          </CommlCoverage>
          <CommlCoverage>
            <CoverageCd>APLUS</CoverageCd>
            <CoverageDesc>Auto Coverage Plus</CoverageDesc>
            <CurrentTermAmt>
              <Amt>66.00</Amt>
            </CurrentTermAmt>
          </CommlCoverage>
        </CommlVeh>
      </CommlRateState>
      <CommlCoverage>
        <CoverageCd>CSL</CoverageCd>
        <Limit>
          <FormatInteger>1000000</FormatInteger>
        </Limit>
      </CommlCoverage>
      <CommlCoverage>
        <CoverageCd>XNUCL</CoverageCd>
        <CoverageDesc>Exclusion - Nuclear Energy Liability</CoverageDesc>
      </CommlCoverage>
      <CommlCoverage>
        <CoverageCd>XPOLU</CoverageCd>
        <CoverageDesc>Amendment of Pollution Exclusion - Pollution Related to Auto</CoverageDesc>
      </CommlCoverage>
      <CommlDriver id="D1_M36">
        <ItemIdInfo>
          <InsurerId>0001</InsurerId>
        </ItemIdInfo>
        <GeneralPartyInfo>
          <NameInfo>
            <PersonName>
              <Surname>SALLY</Surname>
              <GivenName>BRANDSTETTER</GivenName>
            </PersonName>
            <CommlName>
              <CommercialName>BRANDSTETTER SALLY</CommercialName>
            </CommlName>
          </NameInfo>
        </GeneralPartyInfo>
        <DriverInfo>
          <PersonInfo>
            <GenderCd>F</GenderCd>
            <BirthDt isMasked="true">1977-**-**</BirthDt>
          </PersonInfo>
          <DriversLicense>
            <DriversLicenseNumber isMasked="true">8Q7S7********</DriversLicenseNumber>
            <StateProvCd>OR</StateProvCd>
          </DriversLicense>
          <License>
            <LicenseTypeCd>Driver</LicenseTypeCd>
            <LicensePermitNumber isMasked="true">8Q7S7********</LicensePermitNumber>
            <StateProvCd>OR</StateProvCd>
          </License>
          <TaxIdentity>
            <TaxId>AV1-AP-6383</TaxId>
            <TaxIdTypeCd>SSN</TaxIdTypeCd>
          </TaxIdentity>
        </DriverInfo>
      </CommlDriver>
      <CommlDriver id="D2_M36">
        <ItemIdInfo>
          <InsurerId>0002</InsurerId>
        </ItemIdInfo>
        <GeneralPartyInfo>
          <NameInfo>
            <PersonName>
              <Surname>SALLY</Surname>
              <GivenName>BRANDSTETTER</GivenName>
            </PersonName>
            <CommlName>
              <CommercialName>BRANDSTETTER SALLY</CommercialName>
            </CommlName>
          </NameInfo>
        </GeneralPartyInfo>
        <DriverInfo>
          <PersonInfo>
            <GenderCd>M</GenderCd>
            <BirthDt isMasked="true">1977-**-**</BirthDt>
          </PersonInfo>
          <DriversLicense>
            <DriversLicenseNumber isMasked="true">FFCYP********</DriversLicenseNumber>
            <StateProvCd>OR</StateProvCd>
          </DriversLicense>
          <License>
            <LicenseTypeCd>Driver</LicenseTypeCd>
            <LicensePermitNumber isMasked="true">FFCYP********</LicensePermitNumber>
            <StateProvCd>OR</StateProvCd>
          </License>
        </DriverInfo>
      </CommlDriver>
      <Form>
        <FormNumber>ILF1451120</FormNumber>
        <FormName>Oregon Changes - Definition Of Dishonesty</FormName>
        <EditionDt>2020-11-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>AUNN1A16</FormNumber>
        <FormName>Overprint Page</FormName>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CAT4200215</FormNumber>
        <FormName>Auto Coverage Plus Endorsement</FormName>
        <EditionDt>2015-02-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>ILT0010107</FormNumber>
        <FormName>Common Policy Conditions</FormName>
        <EditionDt>2007-01-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CAT0020215</FormNumber>
        <FormName>Business Auto Coverage Part Declarations (item 3)</FormName>
        <EditionDt>2015-02-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>IL01420908</FormNumber>
        <FormName>Oregon Changes - Domestic Partnership</FormName>
        <EditionDt>2008-09-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>PNCB220516</FormNumber>
        <FormName>Important Notice - Rights and Responsibilities - O</FormName>
        <EditionDt>2016-05-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>PNT3920104</FormNumber>
        <FormName>Important Notice To Oregon Policyholders</FormName>
        <EditionDt>2004-01-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>IL02790908</FormNumber>
        <FormName>Oregon Changes - Cancellation and Nonrenewal</FormName>
        <EditionDt>2008-09-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>ILT8010101</FormNumber>
        <FormName>Forms, Endorsements and Schedule Numbers</FormName>
        <EditionDt>2001-01-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CAT4590215</FormNumber>
        <FormName>Amendment Of Employee Definition</FormName>
        <EditionDt>2015-02-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>AUNN2I16</FormNumber>
        <FormName>Policy Cover</FormName>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CAIDOR0796</FormNumber>
        <FormName>Oregon Insurance Identification Card</FormName>
        <EditionDt>1996-07-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CAT0310215</FormNumber>
        <FormName>Table Of Contents Business Auto Coverage Form</FormName>
        <EditionDt>2015-02-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CA22361116</FormNumber>
        <FormName>Oregon Personal Injury Protection</FormName>
        <EditionDt>2016-11-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CAT0030215</FormNumber>
        <FormName>Business Auto Coverage Part Declarations (items 4</FormName>
        <EditionDt>2015-02-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>ILT4270619</FormNumber>
        <FormName>Additional Benefits</FormName>
        <EditionDt>2019-06-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>IL00210908</FormNumber>
        <FormName>Nuclear Energy Liability Exclusion Endorsement (br</FormName>
        <EditionDt>2008-09-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>UIOR100116</FormNumber>
        <FormName>Supplementary Commercial Automobile Application Un</FormName>
        <EditionDt>2016-01-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>AUNN3C17</FormNumber>
        <FormName>Auto Premium Summary</FormName>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CAT3060217</FormNumber>
        <FormName>Stark Rating Modification Report (trmr)</FormName>
        <EditionDt>2017-02-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CAT0300216</FormNumber>
        <FormName>Business Auto/Auto Dealers/Motor Carrier Coverage</FormName>
        <EditionDt>2016-02-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>COVDESC</FormNumber>
        <FormName>Coverage Description</FormName>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>ILT0021189</FormNumber>
        <FormName>Common Policy Declarations</FormName>
        <EditionDt>1989-11-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CAT6250718</FormNumber>
        <FormName>Roadside Assistance Coverage</FormName>
        <EditionDt>2018-07-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>PNCB360918</FormNumber>
        <FormName>Commercial Auto Roadside Assistance Coverage Cards</FormName>
        <EditionDt>2018-09-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CA01491013</FormNumber>
        <FormName>Oregon Changes</FormName>
        <EditionDt>2013-10-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CAT0010215</FormNumber>
        <FormName>Business Auto Coverage Part Declarations (items 1</FormName>
        <EditionDt>2015-02-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CA21050116</FormNumber>
        <FormName>Oregon Uninsured Motorists Coverage - Bodily Injur</FormName>
        <EditionDt>2016-01-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>CA00011013</FormNumber>
        <FormName>Business Auto Coverage Form</FormName>
        <EditionDt>2013-10-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>ILT4120315</FormNumber>
        <FormName>Amendment Of Common Policy Conditions - Prohibited</FormName>
        <EditionDt>2015-03-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
      <Form>
        <FormNumber>PNT4540108</FormNumber>
        <FormName>Important Notice - Independent Agent and Broker Co</FormName>
        <EditionDt>2008-01-01</EditionDt>
        <IterationNumber>001</IterationNumber>
      </Form>
    </CommlAutoLineBusiness>
    <RemarkText IdRef="PolicyLevel_M36">DIRECT BILL
    </RemarkText>
    <PaymentOption>
      <com.safeco_MonthlyPaymentPlan>N</com.safeco_MonthlyPaymentPlan>
      <com.safeco_RecurringPaymentInfo>
        <com.safeco_TokenizedAccountNumber>5843681698134472056</com.safeco_TokenizedAccountNumber>
        <ElectronicFundsTransfer>
          <FromAcct>
            <BankInfo>
              <AccountNumberId isMasked="true">**********5678</AccountNumberId>
              <BankId>*********</BankId>
            </BankInfo>
            <MiscParty>
              <Surname>BOLEN</Surname>
              <GivenName>RICHARD</GivenName>
            </MiscParty>
            <CommercialName>RICHARD BOLEN</CommercialName>
          </FromAcct>
        </ElectronicFundsTransfer>
        <com.safeco_InstrumentId>***********</com.safeco_InstrumentId>
        <AcctTypeCd>
          <MethodOfPayment>Checking</MethodOfPayment>
        </AcctTypeCd>
      </com.safeco_RecurringPaymentInfo>
      <PaymentPlanCd>E</PaymentPlanCd>
      <DayMonthDue>11</DayMonthDue>
      <MethodPaymentCd>EFT</MethodPaymentCd>
    </PaymentOption>
  </CommlAutoPolicyQuoteInqRq>
</InsuranceSvcRq>
</ACORD>