<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .label { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-label { font-family: Arial, sans-serif; font-size: 10px; fill: #34495e; }
      .external { fill: #e8f4fd; stroke: #3498db; stroke-width: 2; }
      .service { fill: #f0f8e8; stroke: #27ae60; stroke-width: 2; }
      .storage { fill: #fdf2e9; stroke: #e67e22; stroke-width: 2; }
      .target { fill: #f4e8fd; stroke: #9b59b6; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" class="title">Supplemental Data Processing - Data Flow Diagram</text>
  
  <!-- External Systems -->
  <g id="external-systems">
    <text x="100" y="70" class="label">External Systems</text>
    
    <!-- ABA Users -->
    <rect x="20" y="80" width="80" height="50" class="external" rx="5"/>
    <text x="60" y="100" text-anchor="middle" class="small-label">ABA</text>
    <text x="60" y="115" text-anchor="middle" class="small-label">Users</text>
    
    <!-- LTU -->
    <rect x="20" y="150" width="80" height="50" class="external" rx="5"/>
    <text x="60" y="170" text-anchor="middle" class="small-label">Large Transfer</text>
    <text x="60" y="185" text-anchor="middle" class="small-label">Utility (LTU)</text>
    
    <!-- AQE -->
    <rect x="20" y="220" width="80" height="50" class="external" rx="5"/>
    <text x="60" y="240" text-anchor="middle" class="small-label">AQE</text>
    <text x="60" y="255" text-anchor="middle" class="small-label">System</text>
    
    <!-- SLIM -->
    <rect x="20" y="290" width="80" height="50" class="external" rx="5"/>
    <text x="60" y="310" text-anchor="middle" class="small-label">SLIM</text>
    <text x="60" y="325" text-anchor="middle" class="small-label">Integration</text>
  </g>
  
  <!-- Presentation Layer -->
  <g id="presentation-layer">
    <text x="250" y="70" class="label">Presentation Layer</text>
    
    <!-- Admin Console -->
    <rect x="180" y="80" width="120" height="60" class="service" rx="5"/>
    <text x="240" y="105" text-anchor="middle" class="small-label">Admin Console</text>
    <text x="240" y="120" text-anchor="middle" class="small-label">UI</text>
    
    <!-- API Gateway -->
    <rect x="180" y="160" width="120" height="60" class="service" rx="5"/>
    <text x="240" y="185" text-anchor="middle" class="small-label">REST API</text>
    <text x="240" y="200" text-anchor="middle" class="small-label">Gateway</text>
  </g>
  
  <!-- Application Layer -->
  <g id="application-layer">
    <text x="500" y="70" class="label">Application Services</text>
    
    <!-- Upload Processing Service -->
    <rect x="380" y="80" width="120" height="60" class="service" rx="5"/>
    <text x="440" y="100" text-anchor="middle" class="small-label">Upload Processing</text>
    <text x="440" y="115" text-anchor="middle" class="small-label">Service</text>
    <text x="440" y="130" text-anchor="middle" class="small-label">(Validation & Parsing)</text>
    
    <!-- Merge Engine Service -->
    <rect x="520" y="80" width="120" height="60" class="service" rx="5"/>
    <text x="580" y="100" text-anchor="middle" class="small-label">Merge Engine</text>
    <text x="580" y="115" text-anchor="middle" class="small-label">Service</text>
    <text x="580" y="130" text-anchor="middle" class="small-label">(Core Logic)</text>
    
    <!-- Mapping Management Service -->
    <rect x="380" y="160" width="120" height="60" class="service" rx="5"/>
    <text x="440" y="180" text-anchor="middle" class="small-label">Mapping Management</text>
    <text x="440" y="195" text-anchor="middle" class="small-label">Service</text>
    <text x="440" y="210" text-anchor="middle" class="small-label">(Config Rules)</text>
    
    <!-- Notification Service -->
    <rect x="520" y="160" width="120" height="60" class="service" rx="5"/>
    <text x="580" y="180" text-anchor="middle" class="small-label">Notification</text>
    <text x="580" y="195" text-anchor="middle" class="small-label">Service</text>
    <text x="580" y="210" text-anchor="middle" class="small-label">(Alerts & Monitoring)</text>
  </g>
  
  <!-- Infrastructure Layer -->
  <g id="infrastructure-layer">
    <text x="500" y="270" class="label">Infrastructure Layer</text>
    
    <!-- Message Queue -->
    <rect x="380" y="280" width="100" height="50" class="storage" rx="5"/>
    <text x="430" y="300" text-anchor="middle" class="small-label">Message Queue</text>
    <text x="430" y="315" text-anchor="middle" class="small-label">(RabbitMQ)</text>
    
    <!-- File Storage -->
    <rect x="500" y="280" width="100" height="50" class="storage" rx="5"/>
    <text x="550" y="300" text-anchor="middle" class="small-label">File Storage</text>
    <text x="550" y="315" text-anchor="middle" class="small-label">(S3/Blob)</text>
    
    <!-- Configuration DB -->
    <rect x="380" y="350" width="100" height="50" class="storage" rx="5"/>
    <text x="430" y="370" text-anchor="middle" class="small-label">Configuration</text>
    <text x="430" y="385" text-anchor="middle" class="small-label">Database</text>
    
    <!-- Redis Cache -->
    <rect x="500" y="350" width="100" height="50" class="storage" rx="5"/>
    <text x="550" y="370" text-anchor="middle" class="small-label">Redis Cache</text>
    <text x="550" y="385" text-anchor="middle" class="small-label">(Session/Config)</text>
  </g>
  
  <!-- Target Systems -->
  <g id="target-systems">
    <text x="800" y="70" class="label">Target Systems</text>
    
    <!-- Opportunity Service -->
    <rect x="750" y="80" width="120" height="60" class="target" rx="5"/>
    <text x="810" y="105" text-anchor="middle" class="small-label">Opportunity</text>
    <text x="810" y="120" text-anchor="middle" class="small-label">Service</text>
    
    <!-- Audit Service -->
    <rect x="750" y="160" width="120" height="60" class="target" rx="5"/>
    <text x="810" y="185" text-anchor="middle" class="small-label">Audit</text>
    <text x="810" y="200" text-anchor="middle" class="small-label">Service</text>
  </g>
  
  <!-- Data Flow Arrows -->
  <!-- ABA to Admin Console -->
  <line x1="100" y1="105" x2="180" y2="110" class="arrow"/>
  <text x="140" y="100" class="small-label">Upload Files</text>
  
  <!-- External Systems to API Gateway -->
  <line x1="100" y1="175" x2="180" y2="185" class="arrow"/>
  <line x1="100" y1="245" x2="180" y2="190" class="arrow"/>
  <line x1="100" y1="315" x2="180" y2="195" class="arrow"/>
  <text x="140" y="240" class="small-label">XML Files</text>
  
  <!-- Admin Console to Upload Service -->
  <line x1="300" y1="110" x2="380" y2="110" class="arrow"/>
  
  <!-- API Gateway to Upload Service -->
  <line x1="300" y1="190" x2="380" y2="120" class="arrow"/>
  
  <!-- Upload Service to Message Queue -->
  <line x1="440" y1="140" x2="430" y2="280" class="arrow"/>
  
  <!-- Upload Service to File Storage -->
  <line x1="500" y1="120" x2="520" y2="300" class="arrow"/>
  
  <!-- Message Queue to Merge Engine -->
  <line x1="480" y1="305" x2="520" y2="120" class="arrow"/>
  
  <!-- Merge Engine to Mapping Service -->
  <line x1="580" y1="140" x2="500" y2="180" class="arrow"/>
  
  <!-- Merge Engine to File Storage -->
  <line x1="580" y1="140" x2="550" y2="280" class="arrow"/>
  
  <!-- Mapping Service to Config DB -->
  <line x1="440" y1="220" x2="430" y2="350" class="arrow"/>
  
  <!-- Merge Engine to Opportunity Service -->
  <line x1="640" y1="110" x2="750" y2="110" class="data-arrow"/>
  <text x="695" y="105" class="small-label">Merged Data</text>
  
  <!-- Notification Service to Audit Service -->
  <line x1="640" y1="190" x2="750" y2="190" class="arrow"/>
  
  <!-- Processing Steps -->
  <g id="processing-steps">
    <text x="600" y="450" class="label">Key Processing Steps:</text>
    
    <circle cx="50" cy="480" r="15" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
    <text x="50" y="485" text-anchor="middle" class="small-label" fill="white">1</text>
    <text x="80" y="485" class="small-label">ABA uploads supplemental CSV/XLSX files</text>
    
    <circle cx="50" cy="510" r="15" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
    <text x="50" y="515" text-anchor="middle" class="small-label" fill="white">2</text>
    <text x="80" y="515" class="small-label">External systems send ACORD XML files</text>
    
    <circle cx="50" cy="540" r="15" fill="#27ae60" stroke="#229954" stroke-width="2"/>
    <text x="50" y="545" text-anchor="middle" class="small-label" fill="white">3</text>
    <text x="80" y="545" class="small-label">Upload service validates and stores files</text>
    
    <circle cx="50" cy="570" r="15" fill="#27ae60" stroke="#229954" stroke-width="2"/>
    <text x="50" y="575" text-anchor="middle" class="small-label" fill="white">4</text>
    <text x="80" y="575" class="small-label">Message queue triggers merge process</text>
    
    <circle cx="50" cy="600" r="15" fill="#27ae60" stroke="#229954" stroke-width="2"/>
    <text x="50" y="605" text-anchor="middle" class="small-label" fill="white">5</text>
    <text x="80" y="605" class="small-label">Merge engine applies mapping rules</text>
    
    <circle cx="50" cy="630" r="15" fill="#9b59b6" stroke="#8e44ad" stroke-width="2"/>
    <text x="50" y="635" text-anchor="middle" class="small-label" fill="white">6</text>
    <text x="80" y="635" class="small-label">Merged data sent to Opportunity Service</text>
    
    <circle cx="50" cy="660" r="15" fill="#9b59b6" stroke="#8e44ad" stroke-width="2"/>
    <text x="50" y="665" text-anchor="middle" class="small-label" fill="white">7</text>
    <text x="80" y="665" class="small-label">Notifications and audit events generated</text>
  </g>
  
  <!-- Legend -->
  <g id="legend">
    <text x="950" y="450" class="label">Legend:</text>
    
    <rect x="920" y="470" width="20" height="15" class="external" rx="2"/>
    <text x="950" y="482" class="small-label">External Systems</text>
    
    <rect x="920" y="495" width="20" height="15" class="service" rx="2"/>
    <text x="950" y="507" class="small-label">Application Services</text>
    
    <rect x="920" y="520" width="20" height="15" class="storage" rx="2"/>
    <text x="950" y="532" class="small-label">Infrastructure</text>
    
    <rect x="920" y="545" width="20" height="15" class="target" rx="2"/>
    <text x="950" y="557" class="small-label">Target Systems</text>
    
    <line x1="920" y1="575" x2="940" y2="575" class="arrow"/>
    <text x="950" y="580" class="small-label">Control Flow</text>
    
    <line x1="920" y1="595" x2="940" y2="595" class="data-arrow"/>
    <text x="950" y="600" class="small-label">Data Flow</text>
  </g>
</svg>
