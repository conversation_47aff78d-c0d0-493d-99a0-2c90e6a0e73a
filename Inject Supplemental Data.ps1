﻿# TO DO:
#
# store injected files into folders, one for each BT Record
# zip files in each folder, labeled by BT Record
# Use Large Transfer Utility to upload zip files
#
# XMl files need to be uploaded one at a time so that the supplementary data doesn't get removed




# Flags
$global:AUTOUPLOAD = $true
$global:PROD_UPLOAD = $true
$global:DEBUG = $false

$STYLESHEET_FOLDER = "C:\Users\<USER>\OneDrive - Liberty Mutual\_XML Stylesheets\Personal Lines"

# XPath Friendly Names
$global:PrimaryInsuredString = "//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']"
$global:LOBCdString = "//PersPolicy/LOBCd"
$global:PolicyNumberString = "//PersPolicy/PolicyNumber"
$global:AgencyId = "//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']/ItemIdInfo/AgencyId"
$global:MetaDataNode = 'com.Safeco_BookTransfer_Metadata'

# Put all Migration Info under this node: /ACORD/InsuranceSvcRq/com.Safeco_MigrationInfo

######################################################################################################################
function Copy_StyleSheets () {
    Param(
        [Parameter(Mandatory=$true)]
        $SourceFolder,
        [Parameter(Mandatory=$true)]
        $DestinationFolder
        )
    Get-ChildItem -Path $SourceFolder -Filter *.xslt -File  | Sort name  | Foreach-Object {
        Copy-Item -Path $_.FullName -Destination $DestinationFolder -Force
        }
}
######################################################################################################################

function Add-XMLAttribute([System.Xml.XmlNode] $Node, $Name, $Value)
{
  $attrib = $Node.OwnerDocument.CreateAttribute($Name)
  $attrib.Value = $Value
  $node.Attributes.Append($attrib)
}
######################################################################################################################
######################################################################################################################
######################################################################################################################
Function Load_AgencyID () {
    Param(        
        [Parameter(Mandatory=$true)]
        $CSVFile
        )
    if ($CSVFile -eq $null) { $CSVFile = Select_File -prompt "Select CSV File containing AgencyID Data" -filter "CSV files (*.csv)|*.csv" }
    if ($CSVFile -ne "") {
        $ComparionData = @(Import-CSV $CSVFile)
        $n = $ComparionData.Count
        write-host "Loaded $n AgencyID records"
        }
    return $ComparionData
}
######################################################################################################################
######################################################################################################################
######################################################################################################################
######################################################################################################################
Function XML_String_With_SingonRq () {
    Param(        
        [Parameter(Mandatory=$true)]
        $SignonRqString
        )

    $guid = New-Guid
    return [xml]@"
<ACORD>
    $SignonRqString
    <InsuranceSvcRq>
        <RqUID>$guid</RqUID>
    </InsuranceSvcRq>
</ACORD>
"@
}
######################################################################################################################
######################################################################################################################
Function Upload_To_AQE_Using_CURL () {
    Param(        
        [Parameter(Mandatory=$true)]
        $FolderName,   
        [Parameter(Mandatory=$true)]
        $FileName,   
        [Parameter(Mandatory=$true)]
        $BT_Code
        )

    if (-not $global:AUTOUPLOAD) { 
        Log -Str "Upload to AQE is not enabled."
        return
        }

    $Fullpath = Join-Path -Path $FolderName -ChildPath $FileName
    Inject_File_AgentBookRollInfo -FullPath $Fullpath -BTCode $BT_Code
#    $global:uploadList.Add($FileName)
    $CurlExecutable = "curl.exe"
    if ($global:PROD_UPLOAD) {
    # PRODUCTION
        $CurlArguments = '--request', 'POST', 
                        'https://uploadservices.pdc.paas.lmig.com/v2/uploadS3',
                        '--header', "'content-type: multipart/form-data'",
                        '--form', "file=@$Fullpath"
                        '-v'
        $UploadTarget = "PROD"
        } else {
    # TEST
        $CurlArguments = '--request', 'POST', 
                        'https://uploadservices-staging.us-east-1.np.paas.lmig.com/uploadS3',
                        '--header', "'content-type: multipart/form-data'",
                        '--form', "file=@$Fullpath"
                        '-v'
        $UploadTarget = "TEST"
        }
    Log -Str "Uploading file $FileName to AQE $UploadTarget..."
    & $CurlExecutable @CurlArguments
    Log -Str "Sleeping for 5 seconds..."
    Start-Sleep -Seconds 5
    Log -Str "Done uploading file $FileName to AQE"

}
######################################################################################################################
######################################################################################################################
# Return a filename based on the contents of the provided ACORD XML Policy String
Function Filename_From_XML ($ACORD_XML) {
    $RatingState = ($ACORD_XML.SelectSingleNode("//Location/Addr/StateProvCd")).InnerXML
    if ($RatingState -eq $null) {
        $RatingState = ($ACORD_XML.SelectSingleNode("//InsuredOrPrincipal/GeneralPartyInfo/Addr/StateProvCd")).InnerXML
    }
    if ($RatingState -eq $null) {
        $RatingState = ($ACORD_XML.SelectSingleNode("//PersPolicy/ControllingStateProvCd")).InnerXML
    }
    $LOB = ($ACORD_XML.SelectSingleNode($global:LOBCdString)).InnerXML
    $PolicyNumber = ($ACORD_XML.SelectSingleNode($global:PolicyNumberString)).InnerXML
    $InsuredName = InsuredName_From_ACORD_XML ($ACORD_XML)
    $ExpDate = ($ACORD_XML.SelectSingleNode("//PersPolicy/ContractTerm/ExpirationDt")).InnerXML
    $legalFilename = "$LOB,$PolicyNumber,$RatingState,$InsuredName,$ExpDate.xml"
    $legalFilename = $legalFilename.Replace("  "," ")
    $legalFilename = $legalFilename -replace ("[{0}]"-f (([System.IO.Path]::GetInvalidFileNameChars()|%{[regex]::Escape($_)}) -join '|')),'_'
    return $legalFilename
}

######################################################################################################################

######################################################################################################################
######################################################################################################################
######################################################################################################################

# $ROOT_FOLDER should point to the main folder with all the supplemental CSV files
#C:\Users\<USER>\OneDrive - Liberty Mutual\Documents\IVANS Bookroll\Comparion.02202025_191251
$ROOT_FOLDER = "C:\Users\<USER>\OneDrive - Liberty Mutual\Documents\IVANS Bookroll\_COMPARION - Special Processing"
$ROOT_FOLDER = "C:\Users\<USER>\OneDrive - Liberty Mutual\Documents\IVANS Bookroll\_MSA Production\Phases 1 and 2"
$WORKING_FOLDER = Join-Path -Path $ROOT_FOLDER -ChildPath "MSA_Gap_(118_renewed).05082025_070934"
$SourceFolder = Join-Path -Path $WORKING_FOLDER -ChildPath "2025-05-08 (Processed XML)"

$DestinationFolder = Join-Path -Path $WORKING_FOLDER -ChildPath "Modified and Injected"
if (!(Test-Path -Path $DestinationFolder)) { [void](New-Item -Path $DestinationFolder -ItemType "Directory") }

$ReportFile = Join-Path -Path $WORKING_FOLDER -ChildPath ("XML Injection Report," + [DateTime]::Now.ToString("yyyyMMdd-HHmmss") + ".csv")
Add-Content -Path $ReportFile  -Value '"Count","PN","Outcome","Filename"'

$SourceDataFile = Join-Path -Path $WORKING_FOLDER -ChildPath "DateModification.csv"


$global:InjectFiles = $true
$global:SaveAllFiles = $false

$zipfiles = $package = $upload = $false

#
# BE SURE TO CHECK THE NAMES OF THE CSV FILE HEADERS TO MAKE SURE THEY MATCH THE CODE BELOW!
#

if ($global:InjectFiles) {

$CSVFile = Join-Path -Path $WORKING_FOLDER -ChildPath "DateModification.csv"
$CSVData = @(Import-CSV $CSVFile)
$n = $CSVData.Count

$headerRow = $CSVData[0]
$header = $headerRow.psobject.properties.name
$keyName = $header[0]

#$ComparionData where-object {($_.$keyName -eq "XPath")} 
$xpaths =  $CSVData[0]
$keyXpath = $xpaths.($header[0])

$columnCount = $header.Count
Write-Output "The CSV file has $columnCount columns."

write-host "Loaded $n records"


$XMLFiles = Get-ChildItem -Path $SourceFolder -Filter *.xml -File  | Sort name
    $totalFiles = $XMLFiles.count
    write-host "There are $totalFiles total files..."
    $i = 0
    Foreach ($file in $XMLFiles) {
        $outcome = ""
        $fileWasInjected = $false
        $theFile = $file.FullName        
        $XmlDocument = [xml] (Get-content $theFile -raw)
        $KEY = ($XmlDocument.SelectSingleNode($keyXpath)).InnerXML

        if ($KEY -eq '39029050') {
            write-host "here"
            }

        $policyRecord = $CSVData | where-object {($_.$keyName -eq $KEY)} 

        if ($policyRecord -ne $null) {

            for ($h = 1; $h -lt $columnCount; $h++) {
                $xpath = $xpaths.($header[$h])
                $newValue = $policyRecord.($header[$h])
                if ($newValue -ne "") { # it could be that some values are empty so don't update them
                    $leafNode = $XmlDocument.SelectSingleNode($xpath)
                    if ($leafNode -ne $null) { 
                        # OK, the node exists so we'll just set the new value
                        $existingValue = $leafNode.InnerText
                        $newValue = $policyRecord.($header[$h])
                        $outcome += '"' + "Set Xpath: " + $xpath + " Replaced value: " + $existingValue + " with " + $newValue + ".`r`n" + '"'
                        write-host $outcome
                        ($XmlDocument.SelectSingleNode($xpath)).InnerXML = $newValue
                        } 
                    else {
                        # node doesn't exist so we need to create it
                        # find out how far back we need to go, e.g.,
                        # //PersPolicy/ContractTerm/EffectiveDt

                        $nodepath = $xpath.Replace('//','')
                        $nodepath = $nodepath.Replace('/','.')
                        #$nodepath = PersPolicy.ContractTerm.EffectiveDt
                        $parts = $nodepath.split(".")
                        # $parts = (PersPolicy, ContractTerm, EffectiveDt)
                        $thePath = "//" + $parts[0]
                        $targetNode = $null
                        # start testing at the missing node's parent node
                        for ($p = $parts.Count - 2; ($p -gt 0) -and ($leafNode -eq $null); $p--) {
                            $testPath = $thePath + "/" + $parts[$p]
                            $leafNode = $XmlDocument.SelectSingleNode($testPath)
                            }
                        # at this point, $leafNode should be valid and $p should be pointing to the good node name (existing), so we just need to create nodes after that
                         for ($q = $p + 2; $q -lt $parts.Count; $q++) {
                            $leafNode = $leafNode.AppendChild($XmlDocument.CreateElement($parts[$q]))
                            }
                        # at this point, $leafNode is the target node where we want to store the new value
                        [void]$leafNode.AppendChild($XmlDocument.CreateTextNode($policyRecord.($header[$h])))

                        $outcome += '"' + "Created new Xpath: " + $xpath + " and set value: " + $newValue + ".`r`n" + '"'
                        write-host $outcome
                        }
                }

                $fileWasInjected = $true
                }
            else {
                $outcome = "Record not found"
                $fileWasInjected = $false
                }
            }

        $newFilename = Filename_From_XML -ACORD_XML $XmlDocument
        $newName = Join-Path -Path $DestinationFolder -ChildPath $newFilename
        if ($global:SaveAllFiles -or $fileWasInjected) {
            $XmlDocument.Save($newName)
            }
        else {
            $outcome += "; file not saved."
            }

        $record = "$i,$KEY,$outcome," + '"' + $newFilename + '"'
        #   Write-Output $record
        Add-Content -Path $ReportFile  -Value $record

        #if ($i -gt 300) { break }
        $i++
        if (($i % 10) -eq 0) { write-host $i.ToString("0000") -ForegroundColor Red -NoNewline }
        else { write-host "." -ForegroundColor Green -NoNewline }
        if (($i % 100) -eq 0) { write-host "" }

        }
}

        # Now package up each BT Record's policies and upload
if ($zipfiles) {
    $BTRecords = @("421959","421961","421962","421963","421964","421965","421966","421967")
    Foreach ($bt in $BTRecords) {
        $path =  $DestinationFolder + "\" + $bt + ",*.xml"
        $ZipName = Join-Path -Path $WORKING_FOLDER -ChildPath ("$bt to upload," + [DateTime]::Now.ToString("yyyyMMdd-HHmmss") + ".zip")

        $compress = @{
            Path = $path
            CompressionLevel = "Fastest"
            DestinationPath = $ZipName
        }
        Compress-Archive @compress
        }
}


if ($package -or $upload) {
    if ($package) { Log -Str "Packaging files..." }
    if ($upload)  { Log -Str "Will be uploading files..." }

        $BTRecords = @("421959","421961","421962","421963","421964","421965","421966","421967")
        Foreach ($bt in $BTRecords) {
            $MaxPerFile = 1000
            $count = 0; $total = 0; $Part = 0;

            $gotSignOn = $false
            $XMLUploadDoc = $null; 
            $filter = "$bt,*.xml"
            $totalFiles = (Get-ChildItem -Path $DestinationFolder -Filter $filter -File).count

            Get-ChildItem -Path $DestinationFolder -Filter $filter -File  | Sort name  | Foreach-Object {
                $specialCharacter = "?"
                $f = $_.BaseName
                $XmlDocument = [xml](Get-Content -Path $_.FullName)

                if (-not $gotSignOn) {
                    # grab the first file and get the SignonRq node to create the template
                    $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
                    $SignonRqText = $SignonRq.OuterXML
                    $guid = New-Guid
                    $template = [xml]@"
<ACORD>
	$SignonRqText
    <InsuranceSvcRq>
        <RqUID>$guid</RqUID>
    </InsuranceSvcRq>
</ACORD>
"@
                    $gotSignOn = $true
                    }

                if ($XMLUploadDoc -eq $null) {
                        $SignonRqText = ""
                        $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
                        if ($SignonRq -ne $null) { $SignonRqText = $SignonRq.OuterXML }
                        $XMLUploadDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                    }
                    if ($count -lt $MaxPerFile) {
                        $count++
                        [void]($XMLUploadDoc.ACORD).AppendChild($XMLUploadDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq"), $true))
                        #write-host "$a AUTOP"
                        $specialCharacter = "p"
                        }
                    else {
                        $total += $count; $Part++;
                        #$XMLUploadDoc = Inject_AgentBookRollInfo -ACORD_XML $XMLUploadDoc -BTCode $bt
                        if ($Part -gt 0) { $filenum = "$Part " } else { $filenum = "" }
                        $filebasename = "BT-$bt, Comparion--Combined $filenum($count PIF).xml"
                        $fname = Join-Path -Path $DestinationFolder -ChildPath $filebasename
                        $XMLUploadDoc.save($fname)
                        write-host
                        Log -Str "Packaged $count policies in file: $filebasename"
                        if ($upload) {
                            Upload_To_AQE_Using_CURL -FolderName $DestinationFolder -FileName $filebasename -BT_Code $bt
                            write-host
                            Log -Str "$filebasename --> Upload file contains $count policies."
                            Log -Str "$filebasename --> Uploaded to AQE (BT-$bt)."
                        }
                        # reset document and add $XMLDocument to it
                        $count = 1; 
                        $XMLUploadDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                        [void]($XMLUploadDoc.ACORD).AppendChild($XMLUploadDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq"), $true))
                        $specialCharacter = "p"
                    }

                $i++
                if (($i % 10) -eq 0) { write-host ([string]::Format("{0:000}", $i)) -ForegroundColor Red -NoNewline }
                else { write-host $specialCharacter -ForegroundColor 'Green' -NoNewline }
                if (($i % 100) -eq 0) { write-host }
                $specialCharacter = ""
                }
        # clean up remaining
        if ($count -gt 0) {
        $Part++
        $XMLUploadDoc = Inject_AgentBookRollInfo -ACORD_XML $XMLUploadDoc -BTCode $bt
        if ($Part -gt 0) { $filenum = "$Part " } else { $filenum = "" }
        $filebasename = "BT-$bt, Comparion--Combined $filenum($count PIF).xml"
        $fname = Join-Path -Path $DestinationFolder -ChildPath $filebasename
        write-host
        Log -Str "Packaged $count policies in file: $filebasename"
        $XMLUploadDoc.save($fname)
        if ($upload) {
            Upload_To_AQE_Using_CURL -FolderName $DestinationFolder -FileName $filebasename -BT_Code $bt
            write-host
            Log -Str "$filebasename --> Upload file contains $count policies."
            Log -Str "$filebasename --> Uploaded to AQE (BT-$bt)."
        }
    }
}
}

write-host
write-host "DONE!"

Invoke-Item $ReportFile
