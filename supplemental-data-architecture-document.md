# Supplemental Data Processing Architecture Document

## Executive Summary

The Supplemental Data Processing Service is designed to automate the complex task of merging supplemental policy data from CSV/XLSX files with ACORD XML files for Book Transfer quoting. This system replaces manual PowerShell scripts with a robust, scalable, event-driven architecture that can process thousands of policies daily while maintaining data integrity and providing comprehensive audit trails.

## Architecture Overview

### Core Design Principles

**Event-Driven Microservices Architecture**: The system employs loosely coupled microservices that communicate through message queues, enabling horizontal scaling and fault tolerance.

**Policy-Centric Data Matching**: The architecture centers around policy number as the primary key for matching supplemental data with XML files, handling the complexity of one-to-many relationships (one supplemental file containing multiple policies, one XML file containing one policy).

**Asynchronous Processing Pipeline**: All heavy processing operations are asynchronous, preventing blocking operations and enabling high throughput.

**Configuration-Driven Mapping**: Field mappings between supplemental data and XML structures are externalized as configurable rules, allowing business users to modify mappings without code changes.

## Data Flow Architecture

### Primary Data Flow

1. **File Ingestion Phase**
   - ABA users upload supplemental CSV/XLSX files through Admin Console
   - External systems (LTU, AQE, SLIM) send ACORD XML files via REST APIs
   - Upload Processing Service validates file formats, schemas, and business rules
   - Files are stored in secure cloud storage with metadata extraction

2. **Matching and Queuing Phase**
   - System extracts policy numbers from both supplemental files and XML files
   - Policy-based matching logic identifies which files belong together
   - Incomplete matches are queued with configurable timeout periods
   - Complete matches trigger merge processing events

3. **Merge Processing Phase**
   - Merge Engine retrieves matching files from storage
   - Mapping Management Service provides current field mapping rules
   - Data transformation applies business logic and validation rules
   - Merged data is validated against business rules and data quality checks

4. **Integration and Notification Phase**
   - Validated merged data is sent to Opportunity Service
   - Success/failure notifications are sent to relevant stakeholders
   - Audit events are logged for compliance and troubleshooting

### Bulk Import Handling Strategy

**Streaming Processing**: Large supplemental files are processed using streaming techniques to handle files with thousands of policy records without memory constraints.

**Batch Optimization**: The system groups related policies for batch processing while maintaining individual policy-level error handling and retry logic.

**Progressive Processing**: Files are processed in chunks, allowing the system to provide progress updates and handle partial failures gracefully.

**Memory Management**: Implements memory-efficient processing patterns using Java NIO.2 and streaming APIs to handle large datasets without OutOfMemoryError exceptions.

## Policy Number Matching Logic

### Matching Algorithm

**Primary Key Extraction**: Policy numbers are extracted from both supplemental files (CSV column) and XML files (XPath: //PersPolicy/PolicyNumber) during the upload phase.

**Normalization**: Policy numbers are normalized (trimmed, case-standardized) to ensure consistent matching across different data sources.

**One-to-Many Relationship Handling**: 
- One supplemental file contains multiple policy numbers (as seen in MSASupplementalData.csv with 7,649 policies)
- One XML file contains exactly one policy number
- System maintains a mapping table of policy numbers to file locations

**Matching States**:
- **Pending**: Supplemental data exists but no matching XML file
- **Ready**: Both supplemental data and XML file are available
- **Processing**: Merge operation is in progress
- **Complete**: Merge completed successfully
- **Failed**: Merge failed after retry attempts
- **Timeout**: No matching file received within configured timeframe

### Matching Implementation

```
Policy Matching Service:
1. Extract policy number from uploaded file
2. Check existing policy registry for matches
3. If match found and both files present: trigger merge
4. If partial match: update registry and set timeout
5. If timeout exceeded: alert and mark as incomplete
```

## Technical Architecture Components

### Upload Processing Service

**File Validation Engine**: Implements comprehensive validation including file format verification, schema validation, virus scanning, and business rule validation.

**Metadata Extraction**: Extracts policy numbers, file properties, upload timestamps, and user information for tracking and matching purposes.

**Storage Management**: Manages secure file storage with encryption at rest, access logging, and automated cleanup based on retention policies.

**Event Publishing**: Publishes file upload events to message queues for downstream processing.

### Merge Engine Service

**Core Processing Logic**: Implements the business logic for combining supplemental data with XML files, handling data type conversions, validation, and error recovery.

**Mapping Rule Application**: Applies configurable field mapping rules to transform supplemental data into appropriate XML structure modifications.

**Data Validation**: Performs comprehensive validation of merged data including business rule validation, data quality checks, and referential integrity verification.

**Error Handling**: Implements sophisticated error handling with categorized errors, automatic retry logic, and escalation procedures.

### Mapping Management Service

**Configuration Storage**: Stores mapping configurations in versioned format with change tracking and rollback capabilities.

**Rule Engine**: Processes mapping rules including field transformations, conditional logic, and data validation rules.

**Hot Reload**: Supports dynamic configuration updates without service restart, enabling rapid business rule changes.

**Template Management**: Provides reusable mapping templates for common scenarios, reducing configuration complexity.

## Shell Script Migration Strategy

### Current Shell Script Functionality Analysis

The existing PowerShell scripts perform several critical functions that must be replicated in the Java-based architecture:

**CSV/XLSX Processing**: Reading and parsing supplemental data files with various formats and schemas.

**XML Manipulation**: Modifying ACORD XML files by inserting, updating, or removing specific nodes based on supplemental data.

**Data Transformation**: Converting data types, applying business rules, and handling edge cases in data mapping.

**Validation Logic**: Implementing business-specific validation rules for data quality and completeness.

**Error Handling**: Managing processing errors, logging issues, and providing meaningful error messages.

### Migration Approach: Java Implementation

**Complete Java Migration**: All shell script functionality will be migrated to Java rather than maintaining shell scripts, providing better performance, maintainability, and integration capabilities.

**Library Selection**:
- **CSV Processing**: OpenCSV for robust CSV parsing with configurable delimiters and encoding
- **Excel Processing**: Apache POI for comprehensive XLSX file handling
- **XML Processing**: JAXB for object binding and DOM/SAX for direct XML manipulation
- **XPath Processing**: Java XPath API for complex XML node selection and modification
- **Data Validation**: Bean Validation (JSR-303) for declarative validation rules

**Functional Mapping**:
- PowerShell CSV Import → Java OpenCSV with custom record processors
- PowerShell Excel processing → Apache POI with streaming for large files
- PowerShell XML manipulation → Java DOM with XPath for precise node targeting
- PowerShell validation → Java Bean Validation with custom validators
- PowerShell error handling → Java exception hierarchy with detailed error categorization

**Performance Optimization**:
- Streaming processing for large files to minimize memory usage
- Parallel processing for independent operations
- Connection pooling for database operations
- Caching for frequently accessed mapping configurations

### Shell Script Logic Integration

**Data Extraction Logic**: Shell script data extraction patterns are replicated using Java streaming APIs with configurable field mappings and data type conversions.

**Business Rule Implementation**: Complex business rules from shell scripts are implemented as configurable Java validators with externalized rule definitions.

**Error Recovery**: Shell script error handling patterns are enhanced with Java exception handling, providing better error categorization and recovery strategies.

**Logging and Monitoring**: Shell script logging is replaced with structured logging using SLF4J with correlation IDs for tracking processing across distributed components.

## Scalability and Performance Architecture

### Horizontal Scaling Strategy

**Auto-scaling Configuration**: Services automatically scale based on queue depth, processing time, and resource utilization metrics.

**Load Distribution**: Message queues distribute work across multiple service instances with configurable routing strategies.

**Database Scaling**: Read replicas for query operations and connection pooling for optimal database utilization.

**File Storage Scaling**: Cloud-native storage with CDN integration for global file distribution and access optimization.

### Performance Optimization

**Asynchronous Processing**: All heavy operations are asynchronous, preventing blocking and enabling high concurrency.

**Caching Strategy**: Multi-level caching including mapping configurations, file metadata, and frequently accessed data.

**Batch Processing**: Intelligent batching of related operations to minimize overhead and improve throughput.

**Resource Management**: Careful resource management with connection pooling, memory optimization, and garbage collection tuning.

## Error Handling and Resilience

### Comprehensive Error Handling

**Error Categorization**: Errors are categorized as transient (network issues, temporary service unavailability) or permanent (data validation failures, configuration errors).

**Retry Logic**: Exponential backoff retry strategy for transient errors with configurable maximum attempts and delay intervals.

**Circuit Breaker Pattern**: Prevents cascade failures by temporarily stopping calls to failing services and providing fallback mechanisms.

**Dead Letter Queues**: Failed messages are routed to dead letter queues for manual investigation and reprocessing.

### Monitoring and Alerting

**Real-time Monitoring**: Comprehensive monitoring of processing times, error rates, queue depths, and system resources.

**Proactive Alerting**: Configurable alerts for various conditions including high error rates, processing delays, and system resource exhaustion.

**Health Checks**: Automated health checks for all services with dependency verification and graceful degradation.

**Audit Logging**: Complete audit trail of all operations for compliance, troubleshooting, and business intelligence.

## Security and Compliance

### Data Security

**Encryption**: Data encrypted at rest and in transit using industry-standard encryption algorithms.

**Access Control**: Role-based access control with principle of least privilege and regular access reviews.

**Audit Trail**: Comprehensive logging of all data access and modifications with immutable audit logs.

**Data Classification**: Automatic identification and handling of sensitive data with appropriate protection measures.

### Compliance Framework

**Regulatory Compliance**: Architecture designed to support SOX, GDPR, and industry-specific compliance requirements.

**Data Retention**: Configurable data retention policies with automated cleanup and archival processes.

**Change Management**: Formal change management process for configuration updates with approval workflows and rollback capabilities.

**Incident Response**: Defined incident response procedures with automated detection and escalation processes.

## Implementation Considerations

### Technology Stack Rationale

**Spring Boot**: Provides comprehensive framework for microservices with excellent integration capabilities and production-ready features.

**RabbitMQ**: Reliable message queuing with dead letter queues, message persistence, and clustering support.

**PostgreSQL**: ACID-compliant database with JSON support for flexible schema evolution and complex queries.

**Redis**: High-performance caching and session storage with clustering and persistence options.

**Cloud Storage**: Scalable, secure file storage with global distribution and automated backup capabilities.

### Development and Deployment

**CI/CD Pipeline**: Automated build, test, and deployment pipeline with quality gates and automated rollback capabilities.

**Infrastructure as Code**: All infrastructure defined as code for consistent deployments and environment management.

**Blue-Green Deployment**: Zero-downtime deployments with automated health checks and rollback procedures.

**Monitoring Integration**: Comprehensive monitoring and logging integration from development through production environments.

## Conclusion

This architecture provides a robust, scalable, and maintainable solution for supplemental data processing that addresses the complex requirements of Book Transfer quoting. The event-driven microservices design enables horizontal scaling while maintaining data consistency and providing comprehensive audit capabilities. The migration from PowerShell scripts to Java-based processing provides significant improvements in performance, reliability, and maintainability while preserving all existing business logic and validation rules.

The policy-centric matching strategy efficiently handles the one-to-many relationship between supplemental files and XML files, while the asynchronous processing pipeline ensures high throughput and responsiveness. The configuration-driven mapping system empowers business users to manage field mappings independently, reducing dependency on technical teams for routine changes.

This architecture positions the organization for future growth while providing immediate benefits in processing speed, error reduction, and operational efficiency.
