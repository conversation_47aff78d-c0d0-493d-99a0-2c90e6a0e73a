﻿# TO DO:
#
# store injected files into folders, one for each BT Record
# zip files in each folder, labeled by BT Record
# Use Large Transfer Utility to upload zip files
#
# XMl files need to be uploaded one at a time so that the supplementary data doesn't get removed



# Flags
$global:AUTOUPLOAD = $true
$global:PROD_UPLOAD = $true
$global:DEBUG = $false

$STYLESHEET_FOLDER = "C:\Users\<USER>\OneDrive - Liberty Mutual\_XML Stylesheets\Personal Lines"

# XPath Friendly Names
$global:PrimaryInsuredString = "//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']"
$global:LOBCdString = "//PersPolicy/LOBCd"
$global:PolicyNumberString = "//PersPolicy/PolicyNumber"
$global:AgencyId = "//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']/ItemIdInfo/AgencyId"
$global:MetaDataNode = 'com.Safeco_BookTransfer_Metadata'

# Put all Migration Info under this node: /ACORD/InsuranceSvcRq/com.Safeco_MigrationInfo

######################################################################################################################
function Copy_StyleSheets () {
    Param(
        [Parameter(Mandatory=$true)]
        $SourceFolder,
        [Parameter(Mandatory=$true)]
        $DestinationFolder
        )
    Get-ChildItem -Path $SourceFolder -Filter *.xslt -File  | Sort name  | Foreach-Object {
        Copy-Item -Path $_.FullName -Destination $DestinationFolder -Force
        }
}
######################################################################################################################

function Add-XMLAttribute([System.Xml.XmlNode] $Node, $Name, $Value)
{
  $attrib = $Node.OwnerDocument.CreateAttribute($Name)
  $attrib.Value = $Value
  $node.Attributes.Append($attrib)
}
######################################################################################################################
######################################################################################################################
######################################################################################################################
Function Load_AgencyID () {
    Param(        
        [Parameter(Mandatory=$true)]
        $CSVFile
        )
    if ($CSVFile -eq $null) { $CSVFile = Select_File -prompt "Select CSV File containing AgencyID Data" -filter "CSV files (*.csv)|*.csv" }
    if ($CSVFile -ne "") {
        $ComparionData = @(Import-CSV $CSVFile)
        $n = $ComparionData.Count
        write-host "Loaded $n AgencyID records"
        }
    return $ComparionData
}
######################################################################################################################
######################################################################################################################
######################################################################################################################
######################################################################################################################
Function XML_String_With_SingonRq () {
    Param(        
        [Parameter(Mandatory=$true)]
        $SignonRqString
        )

    $guid = New-Guid
    return [xml]@"
<ACORD>
    $SignonRqString
    <InsuranceSvcRq>
        <RqUID>$guid</RqUID>
    </InsuranceSvcRq>
</ACORD>
"@
}
######################################################################################################################
######################################################################################################################
Function Upload_To_AQE_Using_CURL () {
    Param(        
        [Parameter(Mandatory=$true)]
        $FolderName,   
        [Parameter(Mandatory=$true)]
        $FileName,   
        [Parameter(Mandatory=$true)]
        $BT_Code
        )

    if (-not $global:AUTOUPLOAD) { 
        Log -Str "Upload to AQE is not enabled."
        return
        }

    $Fullpath = Join-Path -Path $FolderName -ChildPath $FileName
    Inject_File_AgentBookRollInfo -FullPath $Fullpath -BTCode $BT_Code
#    $global:uploadList.Add($FileName)
    $CurlExecutable = "curl.exe"
    if ($global:PROD_UPLOAD) {
    # PRODUCTION
        $CurlArguments = '--request', 'POST', 
                        'https://uploadservices.pdc.paas.lmig.com/v2/uploadS3',
                        '--header', "'content-type: multipart/form-data'",
                        '--form', "file=@$Fullpath"
                        '-v'
        $UploadTarget = "PROD"
        } else {
    # TEST
        $CurlArguments = '--request', 'POST', 
                        'https://uploadservices-staging.us-east-1.np.paas.lmig.com/uploadS3',
                        '--header', "'content-type: multipart/form-data'",
                        '--form', "file=@$Fullpath"
                        '-v'
        $UploadTarget = "TEST"
        }
    Log -Str "Uploading file $FileName to AQE $UploadTarget..."
    & $CurlExecutable @CurlArguments
    Log -Str "Sleeping for 5 seconds..."
    Start-Sleep -Seconds 5
    Log -Str "Done uploading file $FileName to AQE"

}
######################################################################################################################

######################################################################################################################
######################################################################################################################
######################################################################################################################

# $ROOT_FOLDER should point to the main folder with all the supplemental CSV files
#C:\Users\<USER>\OneDrive - Liberty Mutual\Documents\IVANS Bookroll\Comparion.02202025_191251
$ROOT_FOLDER = "C:\Users\<USER>\OneDrive - Liberty Mutual\Documents\IVANS Bookroll\_COMPARION - Special Processing"
$WORKING_FOLDER = $ROOT_FOLDER 

$WORKING_FOLDER = Join-Path -Path $ROOT_FOLDER -ChildPath "Comparion.06032025_170011"
$SourceFolder = Join-Path -Path $WORKING_FOLDER -ChildPath "2025-06-03 (Processed XML)"

$DestinationFolder = Join-Path -Path $WORKING_FOLDER -ChildPath "2025-06-03 (Injected)"
if (!(Test-Path -Path $DestinationFolder)) { [void](New-Item -Path $DestinationFolder -ItemType "Directory") }

$ReportFile = Join-Path -Path $WORKING_FOLDER -ChildPath ("Comparion Injection Report," + [DateTime]::Now.ToString("yyyyMMdd-HHmmss") + ".csv")
Add-Content -Path $ReportFile  -Value '"Count","PN","Outcome","Filename"'

$injectfiles = $true
$package = $false
$upload = $false
$zipfiles = $false

#
# BE SURE TO CHECK THE NAMES OF THE CSV FILE HEADERS TO MAKE SURE THEY MATCH THE CODE BELOW!
#

if ($injectfiles) {

$CSVFile = Join-Path -Path $ROOT_FOLDER -ChildPath "COMPARION INJECTION DATA.csv"
$ComparionData = @(Import-CSV $CSVFile)
$n = $ComparionData.Count
write-host "Loaded $n Comparion records"

$XMLFiles = Get-ChildItem -Path $SourceFolder -Filter *.xml -File  | Sort name
    $totalFiles = $XMLFiles.count
    write-host "There are $totalFiles total files..."
    $i = 0
    Foreach ($file in $XMLFiles) {
        $outcome = "Record Not Found"
        $theFile = $file.FullName     
        $f = "NOT FOUND"   
        $XmlDocument = [xml] (Get-content $theFile -raw)
        $PN = ($XmlDocument.SelectSingleNode("//PolicyNumber")).InnerXML

        if ($PN -eq '38119801') {
            write-host "here"
            }

        $policyRecord = $ComparionData | where-object {($_.POLICY_NUMBER -eq $PN)} 

        if ($policyRecord -ne $null) {

            $InsuranceSvcRqNode = $XmlDocument.SelectSingleNode('/ACORD/InsuranceSvcRq')

            $MetadataNode = $InsuranceSvcRqNode.AppendChild($XmlDocument.CreateElement('com.Safeco_MigrationInfo'));

            $EnterpriseIdNode = $MetadataNode.AppendChild($XmlDocument.CreateElement('EnterpriseId'));
            [void]$EnterpriseIdNode.AppendChild($XmlDocument.CreateTextNode($policyRecord.PRODUCER_CODE));

            $EnterpriseIdNode = $MetadataNode.AppendChild($XmlDocument.CreateElement('LibertyAffinityNumber'));
            [void]$EnterpriseIdNode.AppendChild($XmlDocument.CreateTextNode($policyRecord.AFFINITY_PARTNER));

            $EnterpriseIdNode = $MetadataNode.AppendChild($XmlDocument.CreateElement('ProducerNPNNumber'));
            [void]$EnterpriseIdNode.AppendChild($XmlDocument.CreateTextNode($policyRecord.NPN));

            $EnterpriseIdNode = $MetadataNode.AppendChild($XmlDocument.CreateElement('ProducerName'));
            [void]$EnterpriseIdNode.AppendChild($XmlDocument.CreateTextNode($policyRecord.AGENT_NAME));

            $EnterpriseIdNode = $MetadataNode.AppendChild($XmlDocument.CreateElement('RepOfficeCd'));
            [void]$EnterpriseIdNode.AppendChild($XmlDocument.CreateTextNode($policyRecord.OFFC_NBR));

            $EnterpriseIdNode = $MetadataNode.AppendChild($XmlDocument.CreateElement('LibertyRepNumber'));
            [void]$EnterpriseIdNode.AppendChild($XmlDocument.CreateTextNode($policyRecord.LIBERTY_REP_NO));

            $EnterpriseIdNode = $MetadataNode.AppendChild($XmlDocument.CreateElement('CombinedLibertyRepAffinityForCompensation'));
            [void]$EnterpriseIdNode.AppendChild($XmlDocument.CreateTextNode($policyRecord.COMBINED_FOR_COMPENSATION));


            #  /ACORD/InsuranceSvcRq/PersPolicy/com.safeco_BookTransferProgram - Set HMST for every policy    
            $PolicyNode = $XmlDocument.SelectSingleNode('//PersPolicy')
            $BookTransferProgramNode = $PolicyNode.AppendChild($XmlDocument.CreateElement('com.safeco_BookTransferProgram'));
            [void]$BookTransferProgramNode.AppendChild($XmlDocument.CreateTextNode($policyRecord.TRANSFER_CD));

            $outcome = "Data injected"

            if ($policyRecord.APPLICANT_EMAIL -ne $null) {
                $PrimaryInsuredNode = $XmlDocument.SelectSingleNode($global:PrimaryInsuredString)
                $GeneralPartyInfoNode = $PrimaryInsuredNode.GeneralPartyInfo
                if ($GeneralPartyInfoNode -eq $null) { $GeneralPartyInfoNode = $PolicyNode.AppendChild($XmlDocument.CreateElement('GeneralPartyInfo')) }
                $CommunicationsNode = $GeneralPartyInfoNode.Communications
                if ($CommunicationsNode -eq $null) { $CommunicationsNode = $GeneralPartyInfoNode.AppendChild($XmlDocument.CreateElement('Communications')) }
                $EmailInfoNode = $CommunicationsNode.EmailInfo
                if ($EmailInfoNode -eq $null) { $EmailInfoNode = $CommunicationsNode.AppendChild($XmlDocument.CreateElement('EmailInfo')) }
                $EmailAddrNode = $EmailInfoNode.EmailInfo
                if ($EmailAddrNode -eq $null) { $EmailAddrNode = $EmailInfoNode.AppendChild($XmlDocument.CreateElement('EmailAddr')) }
                [void]$EmailAddrNode.AppendChild($XmlDocument.CreateTextNode($policyRecord.APPLICANT_EMAIL));
                $outcome += "; email injected"
                } 
                else {
                    $outcome += "; No email to inject"
                    }
            $f = $policyRecord.BT_RECORD

            }

        $f += "," + $file.BaseName + $file.Extension

        $newName = Join-Path -Path $DestinationFolder -ChildPath $f
        $XmlDocument.Save($newName)

        $record = "$i,$PN,$outcome,$f"
        #   Write-Output $record
        Add-Content -Path $ReportFile  -Value $record

        #if ($i -gt 300) { break }
        $i++
        if (($i % 10) -eq 0) { write-host $i.ToString("0000") -ForegroundColor Red -NoNewline }
        else { write-host "." -ForegroundColor Green -NoNewline }
        if (($i % 100) -eq 0) { write-host "" }

        }
}

        # Now package up each BT Record's policies and upload
if ($zipfiles) {
    $BTRecords = @("421959","421961","421962","421963","421964","421965","421966","421967")
    Foreach ($bt in $BTRecords) {
        $path =  $DestinationFolder + "\" + $bt + ",*.xml"
        $ZipName = Join-Path -Path $WORKING_FOLDER -ChildPath ("$bt to upload," + [DateTime]::Now.ToString("yyyyMMdd-HHmmss") + ".zip")

        $compress = @{
            Path = $path
            CompressionLevel = "Fastest"
            DestinationPath = $ZipName
        }
        Compress-Archive @compress
        }
}


if ($package -or $upload) {
    if ($package) { Log -Str "Packaging files..." }
    if ($upload)  { Log -Str "Will be uploading files..." }

        $BTRecords = @("421959","421961","421962","421963","421964","421965","421966","421967")
        Foreach ($bt in $BTRecords) {
            $MaxPerFile = 1000
            $count = 0; $total = 0; $Part = 0;

            $gotSignOn = $false
            $XMLUploadDoc = $null; 
            $filter = "$bt,*.xml"
            $totalFiles = (Get-ChildItem -Path $DestinationFolder -Filter $filter -File).count

            Get-ChildItem -Path $DestinationFolder -Filter $filter -File  | Sort name  | Foreach-Object {
                $specialCharacter = "?"
                $f = $_.BaseName
                $XmlDocument = [xml](Get-Content -Path $_.FullName)

                if (-not $gotSignOn) {
                    # grab the first file and get the SignonRq node to create the template
                    $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
                    $SignonRqText = $SignonRq.OuterXML
                    $guid = New-Guid
                    $template = [xml]@"
<ACORD>
	$SignonRqText
    <InsuranceSvcRq>
        <RqUID>$guid</RqUID>
    </InsuranceSvcRq>
</ACORD>
"@
                    $gotSignOn = $true
                    }

                if ($XMLUploadDoc -eq $null) {
                        $SignonRqText = ""
                        $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
                        if ($SignonRq -ne $null) { $SignonRqText = $SignonRq.OuterXML }
                        $XMLUploadDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                    }
                    if ($count -lt $MaxPerFile) {
                        $count++
                        [void]($XMLUploadDoc.ACORD).AppendChild($XMLUploadDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq"), $true))
                        #write-host "$a AUTOP"
                        $specialCharacter = "p"
                        }
                    else {
                        $total += $count; $Part++;
                        #$XMLUploadDoc = Inject_AgentBookRollInfo -ACORD_XML $XMLUploadDoc -BTCode $bt
                        if ($Part -gt 0) { $filenum = "$Part " } else { $filenum = "" }
                        $filebasename = "BT-$bt, Comparion--Combined $filenum($count PIF).xml"
                        $fname = Join-Path -Path $DestinationFolder -ChildPath $filebasename
                        $XMLUploadDoc.save($fname)
                        write-host
                        Log -Str "Packaged $count policies in file: $filebasename"
                        if ($upload) {
                            Upload_To_AQE_Using_CURL -FolderName $DestinationFolder -FileName $filebasename -BT_Code $bt
                            write-host
                            Log -Str "$filebasename --> Upload file contains $count policies."
                            Log -Str "$filebasename --> Uploaded to AQE (BT-$bt)."
                        }
                        # reset document and add $XMLDocument to it
                        $count = 1; 
                        $XMLUploadDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                        [void]($XMLUploadDoc.ACORD).AppendChild($XMLUploadDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq"), $true))
                        $specialCharacter = "p"
                    }

                $i++
                if (($i % 10) -eq 0) { write-host ([string]::Format("{0:000}", $i)) -ForegroundColor Red -NoNewline }
                else { write-host $specialCharacter -ForegroundColor 'Green' -NoNewline }
                if (($i % 100) -eq 0) { write-host }
                $specialCharacter = ""
                }
        # clean up remaining
        if ($count -gt 0) {
        $Part++
        $XMLUploadDoc = Inject_AgentBookRollInfo -ACORD_XML $XMLUploadDoc -BTCode $bt
        if ($Part -gt 0) { $filenum = "$Part " } else { $filenum = "" }
        $filebasename = "BT-$bt, Comparion--Combined $filenum($count PIF).xml"
        $fname = Join-Path -Path $DestinationFolder -ChildPath $filebasename
        write-host
        Log -Str "Packaged $count policies in file: $filebasename"
        $XMLUploadDoc.save($fname)
        if ($upload) {
            Upload_To_AQE_Using_CURL -FolderName $DestinationFolder -FileName $filebasename -BT_Code $bt
            write-host
            Log -Str "$filebasename --> Upload file contains $count policies."
            Log -Str "$filebasename --> Uploaded to AQE (BT-$bt)."
        }
    }
}
}

write-host
write-host "DONE!"

Invoke-Item $ReportFile
